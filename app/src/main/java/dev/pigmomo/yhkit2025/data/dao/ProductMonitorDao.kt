package dev.pigmomo.yhkit2025.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.CartMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitorRecordEntity
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 商品数据访问对象
 */
@Dao
interface ProductDao {
    @Query("SELECT * FROM products")
    fun getAllProducts(): Flow<List<ProductEntity>>
    
    @Query("SELECT * FROM products WHERE product_id = :productId")
    fun getProductById(productId: String): Flow<ProductEntity?>
    
    @Query("SELECT * FROM products WHERE product_id = :productId")
    suspend fun getProductByIdSync(productId: String): ProductEntity?
    
    @Query("SELECT * FROM products WHERE product_name LIKE '%' || :name || '%'")
    fun searchProductsByName(name: String): Flow<List<ProductEntity>>
    
    @Query("SELECT * FROM products WHERE category = :category")
    fun getProductsByCategory(category: String): Flow<List<ProductEntity>>
    
    @Query("SELECT * FROM products WHERE brand = :brand")
    fun getProductsByBrand(brand: String): Flow<List<ProductEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: ProductEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProducts(products: List<ProductEntity>)
    
    @Update
    suspend fun updateProduct(product: ProductEntity)
    
    @Delete
    suspend fun deleteProduct(product: ProductEntity)
    
    @Query("DELETE FROM products WHERE product_id = :productId")
    suspend fun deleteProductById(productId: String)
    
    @Query("DELETE FROM products")
    suspend fun deleteAllProducts()
    
    @Query("SELECT COUNT(*) FROM products")
    suspend fun getProductCount(): Int
}

/**
 * 商品监控数据访问对象
 */
@Dao
interface ProductMonitorDao {
    @Query("SELECT * FROM product_monitors")
    fun getAllProductMonitors(): Flow<List<ProductMonitorEntity>>
    
    @Query("SELECT * FROM product_monitors WHERE monitor_id = :monitorId")
    fun getProductMonitorById(monitorId: Long): Flow<ProductMonitorEntity?>
    
    @Query("SELECT * FROM product_monitors WHERE product_id = :productId")
    fun getProductMonitorsByProductId(productId: String): Flow<List<ProductMonitorEntity>>
    
    @Query("SELECT * FROM product_monitors WHERE user_token = :userToken")
    fun getProductMonitorsByUserToken(userToken: String): Flow<List<ProductMonitorEntity>>
    
    @Query("SELECT * FROM product_monitors WHERE monitor_status = :status")
    fun getProductMonitorsByStatus(status: String): Flow<List<ProductMonitorEntity>>
    
    @Query("SELECT * FROM product_monitors WHERE monitor_status = 'ACTIVE'")
    fun getActiveProductMonitors(): Flow<List<ProductMonitorEntity>>
    
    @Query("SELECT * FROM product_monitors WHERE user_token = :userToken AND product_id = :productId")
    suspend fun getProductMonitorByUserAndProduct(userToken: String, productId: String): ProductMonitorEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProductMonitor(monitor: ProductMonitorEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProductMonitors(monitors: List<ProductMonitorEntity>)
    
    @Update
    suspend fun updateProductMonitor(monitor: ProductMonitorEntity)
    
    @Delete
    suspend fun deleteProductMonitor(monitor: ProductMonitorEntity)
    
    @Query("DELETE FROM product_monitors WHERE monitor_id = :monitorId")
    suspend fun deleteProductMonitorById(monitorId: Long)
    
    @Query("DELETE FROM product_monitors WHERE user_token = :userToken")
    suspend fun deleteProductMonitorsByUserToken(userToken: String)
    
    @Query("DELETE FROM product_monitors")
    suspend fun deleteAllProductMonitors()
    
    @Query("UPDATE product_monitors SET last_check_time = :checkTime, last_check_result = :result WHERE monitor_id = :monitorId")
    suspend fun updateLastCheckInfo(monitorId: Long, checkTime: Date, result: String)
    
    @Query("SELECT COUNT(*) FROM product_monitors")
    suspend fun getProductMonitorCount(): Int
}

/**
 * 购物车监控数据访问对象
 */
@Dao
interface CartMonitorDao {
    @Query("SELECT * FROM cart_monitors")
    fun getAllCartMonitors(): Flow<List<CartMonitorEntity>>

    @Query("SELECT * FROM cart_monitors WHERE cart_monitor_id = :cartMonitorId")
    fun getCartMonitorById(cartMonitorId: Long): Flow<CartMonitorEntity?>

    @Query("SELECT * FROM cart_monitors WHERE product_id = :productId")
    fun getCartMonitorsByProductId(productId: String): Flow<List<CartMonitorEntity>>

    @Query("SELECT * FROM cart_monitors WHERE user_token = :userToken")
    fun getCartMonitorsByUserToken(userToken: String): Flow<List<CartMonitorEntity>>

    @Query("SELECT * FROM cart_monitors WHERE cart_id = :cartId")
    fun getCartMonitorsByCartId(cartId: String): Flow<List<CartMonitorEntity>>

    @Query("SELECT * FROM cart_monitors WHERE monitor_status = :status")
    fun getCartMonitorsByStatus(status: String): Flow<List<CartMonitorEntity>>

    @Query("SELECT * FROM cart_monitors WHERE monitor_status = 'ACTIVE'")
    fun getActiveCartMonitors(): Flow<List<CartMonitorEntity>>

    @Query("SELECT * FROM cart_monitors WHERE user_token = :userToken AND product_id = :productId")
    suspend fun getCartMonitorByUserAndProduct(userToken: String, productId: String): CartMonitorEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCartMonitor(monitor: CartMonitorEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCartMonitors(monitors: List<CartMonitorEntity>)

    @Update
    suspend fun updateCartMonitor(monitor: CartMonitorEntity)

    @Delete
    suspend fun deleteCartMonitor(monitor: CartMonitorEntity)

    @Query("DELETE FROM cart_monitors WHERE cart_monitor_id = :cartMonitorId")
    suspend fun deleteCartMonitorById(cartMonitorId: Long)

    @Query("DELETE FROM cart_monitors WHERE user_token = :userToken")
    suspend fun deleteCartMonitorsByUserToken(userToken: String)

    @Query("DELETE FROM cart_monitors")
    suspend fun deleteAllCartMonitors()

    @Query("UPDATE cart_monitors SET last_check_time = :checkTime, last_check_result = :result WHERE cart_monitor_id = :cartMonitorId")
    suspend fun updateLastCheckInfo(cartMonitorId: Long, checkTime: Date, result: String)

    @Query("SELECT COUNT(*) FROM cart_monitors")
    suspend fun getCartMonitorCount(): Int
}

/**
 * 监控记录数据访问对象
 */
@Dao
interface MonitorRecordDao {
    @Query("SELECT * FROM monitor_records ORDER BY record_time DESC")
    fun getAllMonitorRecords(): Flow<List<MonitorRecordEntity>>

    @Query("SELECT * FROM monitor_records WHERE record_id = :recordId")
    fun getMonitorRecordById(recordId: Long): Flow<MonitorRecordEntity?>

    @Query("SELECT * FROM monitor_records WHERE product_id = :productId ORDER BY record_time DESC")
    fun getMonitorRecordsByProductId(productId: String): Flow<List<MonitorRecordEntity>>

    @Query("SELECT * FROM monitor_records WHERE user_token = :userToken ORDER BY record_time DESC")
    fun getMonitorRecordsByUserToken(userToken: String): Flow<List<MonitorRecordEntity>>

    @Query("SELECT * FROM monitor_records WHERE monitor_type = :monitorType ORDER BY record_time DESC")
    fun getMonitorRecordsByType(monitorType: String): Flow<List<MonitorRecordEntity>>

    @Query("SELECT * FROM monitor_records WHERE event_type = :eventType ORDER BY record_time DESC")
    fun getMonitorRecordsByEventType(eventType: String): Flow<List<MonitorRecordEntity>>

    @Query("SELECT * FROM monitor_records WHERE record_time BETWEEN :startTime AND :endTime ORDER BY record_time DESC")
    fun getMonitorRecordsByTimeRange(startTime: Date, endTime: Date): Flow<List<MonitorRecordEntity>>

    @Query("SELECT * FROM monitor_records WHERE product_id = :productId AND record_time BETWEEN :startTime AND :endTime ORDER BY record_time DESC")
    fun getMonitorRecordsByProductAndTimeRange(productId: String, startTime: Date, endTime: Date): Flow<List<MonitorRecordEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMonitorRecord(record: MonitorRecordEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMonitorRecords(records: List<MonitorRecordEntity>)

    @Update
    suspend fun updateMonitorRecord(record: MonitorRecordEntity)

    @Delete
    suspend fun deleteMonitorRecord(record: MonitorRecordEntity)

    @Query("DELETE FROM monitor_records WHERE record_id = :recordId")
    suspend fun deleteMonitorRecordById(recordId: Long)

    @Query("DELETE FROM monitor_records WHERE product_id = :productId")
    suspend fun deleteMonitorRecordsByProductId(productId: String)

    @Query("DELETE FROM monitor_records WHERE user_token = :userToken")
    suspend fun deleteMonitorRecordsByUserToken(userToken: String)

    @Query("DELETE FROM monitor_records WHERE record_time < :beforeTime")
    suspend fun deleteMonitorRecordsBeforeTime(beforeTime: Date)

    @Query("DELETE FROM monitor_records")
    suspend fun deleteAllMonitorRecords()

    @Query("SELECT COUNT(*) FROM monitor_records")
    suspend fun getMonitorRecordCount(): Int

    @Query("SELECT COUNT(*) FROM monitor_records WHERE product_id = :productId")
    suspend fun getMonitorRecordCountByProductId(productId: String): Int
}
