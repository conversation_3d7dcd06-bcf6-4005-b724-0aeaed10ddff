package dev.pigmomo.yhkit2025.data.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 商品实体类
 * 存储商品的基本信息
 */
@Entity(
    tableName = "products",
    indices = [
        Index("product_name"), // 加快基于商品名称的查询
        Index("category"), // 加快基于分类的查询
        Index("brand"), // 加快基于品牌的查询
        Index("create_time") // 加快基于创建时间的查询
    ]
)
data class ProductEntity(
    /**
     * 商品ID，主键
     */
    @PrimaryKey
    @ColumnInfo(name = "product_id")
    val productId: String,
    
    /**
     * 商品名称
     */
    @ColumnInfo(name = "product_name")
    val productName: String,
    
    /**
     * 商品描述
     */
    @ColumnInfo(name = "description")
    val description: String = "",
    
    /**
     * 商品分类
     */
    @ColumnInfo(name = "category")
    val category: String = "",
    
    /**
     * 商品品牌
     */
    @ColumnInfo(name = "brand")
    val brand: String = "",
    
    /**
     * 商品图片URL
     */
    @ColumnInfo(name = "image_url")
    val imageUrl: String = "",
    
    /**
     * 当前价格
     */
    @ColumnInfo(name = "current_price")
    val currentPrice: Double = 0.0,
    
    /**
     * 原价
     */
    @ColumnInfo(name = "original_price")
    val originalPrice: Double = 0.0,
    
    /**
     * 库存数量
     */
    @ColumnInfo(name = "stock_quantity")
    val stockQuantity: Int = 0,
    
    /**
     * 是否有库存
     */
    @ColumnInfo(name = "in_stock")
    val inStock: Boolean = true,
    
    /**
     * 是否在清仓
     */
    @ColumnInfo(name = "is_clearance")
    val isClearance: Boolean = false,
    
    /**
     * 商品状态 (NORMAL, OUT_OF_STOCK, CLEARANCE, DISCONTINUED)
     */
    @ColumnInfo(name = "status")
    val status: String = "NORMAL",
    
    /**
     * 商品详情页URL
     */
    @ColumnInfo(name = "detail_url")
    val detailUrl: String = "",
    
    /**
     * 店铺ID
     */
    @ColumnInfo(name = "store_id")
    val storeId: String = "",
    
    /**
     * 店铺名称
     */
    @ColumnInfo(name = "store_name")
    val storeName: String = "",
    
    /**
     * 创建时间
     */
    @ColumnInfo(name = "create_time")
    val createTime: Date = Date(),
    
    /**
     * 最后更新时间
     */
    @ColumnInfo(name = "update_time")
    val updateTime: Date = Date(),
    
    /**
     * 备注信息
     */
    @ColumnInfo(name = "notes")
    val notes: String = ""
)

/**
 * 商品状态枚举
 */
enum class ProductStatus {
    NORMAL,      // 正常
    OUT_OF_STOCK, // 缺货
    CLEARANCE,   // 清仓
    DISCONTINUED // 停产
}
