package dev.pigmomo.yhkit2025.data.model.productmonitor

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 商品监控实体类
 * 存储商品监控配置和TokenEntity信息
 */
@Entity(
    tableName = "product_monitors",
    foreignKeys = [
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["product_id"],
            childColumns = ["product_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("product_id"), // 加快基于商品ID的查询
        Index("user_token"), // 加快基于用户令牌的查询
        Index("monitor_status"), // 加快基于监控状态的查询
        Index("create_time"), // 加快基于创建时间的查询
        Index("last_check_time") // 加快基于最后检查时间的查询
    ]
)
data class ProductMonitorEntity(
    /**
     * 监控记录ID，主键
     */
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "monitor_id")
    val monitorId: Long = 0,
    
    /**
     * 商品ID
     */
    @ColumnInfo(name = "product_id")
    val productId: String,
    
    /**
     * 用户令牌UID（关联TokenEntity）
     */
    @ColumnInfo(name = "user_token")
    val userToken: String,
    
    /**
     * 用户手机号
     */
    @ColumnInfo(name = "phone_number")
    val phoneNumber: String = "",
    
    /**
     * 监控状态 (ACTIVE, PAUSED, STOPPED)
     */
    @ColumnInfo(name = "monitor_status")
    val monitorStatus: String = "ACTIVE",
    
    /**
     * 监控类型 (PRICE, STOCK, BOTH)
     */
    @ColumnInfo(name = "monitor_type")
    val monitorType: String = "BOTH",
    
    /**
     * 目标价格（分）- 价格监控时使用
     */
    @ColumnInfo(name = "target_price")
    val targetPrice: Int = 0,
    
    /**
     * 价格变化阈值（分）- 价格变化超过此值时触发通知
     */
    @ColumnInfo(name = "price_threshold")
    val priceThreshold: Int = 0,
    
    /**
     * 是否监控库存
     */
    @ColumnInfo(name = "monitor_stock")
    val monitorStock: Boolean = true,
    
    /**
     * 是否监控价格
     */
    @ColumnInfo(name = "monitor_price")
    val monitorPrice: Boolean = true,
    
    /**
     * 监控间隔（分钟）
     */
    @ColumnInfo(name = "check_interval")
    val checkInterval: Int = 30,
    
    /**
     * 是否启用通知
     */
    @ColumnInfo(name = "notification_enabled")
    val notificationEnabled: Boolean = true,
    
    /**
     * 通知方式 (APP, EMAIL, SMS)
     */
    @ColumnInfo(name = "notification_type")
    val notificationType: String = "APP",
    
    /**
     * 最后检查时间
     */
    @ColumnInfo(name = "last_check_time")
    val lastCheckTime: Date? = null,
    
    /**
     * 最后检查结果
     */
    @ColumnInfo(name = "last_check_result")
    val lastCheckResult: String = "",
    
    /**
     * 创建时间
     */
    @ColumnInfo(name = "create_time")
    val createTime: Date = Date(),
    
    /**
     * 更新时间
     */
    @ColumnInfo(name = "update_time")
    val updateTime: Date = Date(),
    
    /**
     * 备注信息
     */
    @ColumnInfo(name = "notes")
    val notes: String = ""
)
