package dev.pigmomo.yhkit2025.data.repository

import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.CartMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitorRecordEntity
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 商品监控数据仓库接口
 */
interface ProductMonitorRepository {
    
    // 商品相关操作
    fun getAllProducts(): Flow<List<ProductEntity>>
    fun getProductById(productId: String): Flow<ProductEntity?>
    suspend fun getProductByIdSync(productId: String): ProductEntity?
    fun searchProductsByName(name: String): Flow<List<ProductEntity>>
    fun getProductsByCategory(category: String): Flow<List<ProductEntity>>
    fun getProductsByBrand(brand: String): Flow<List<ProductEntity>>
    suspend fun insertProduct(product: ProductEntity)
    suspend fun insertProducts(products: List<ProductEntity>)
    suspend fun updateProduct(product: ProductEntity)
    suspend fun deleteProduct(product: ProductEntity)
    suspend fun deleteProductById(productId: String)
    suspend fun deleteAllProducts()
    suspend fun getProductCount(): Int
    
    // 商品监控相关操作
    fun getAllProductMonitors(): Flow<List<ProductMonitorEntity>>
    fun getProductMonitorById(monitorId: Long): Flow<ProductMonitorEntity?>
    fun getProductMonitorsByProductId(productId: String): Flow<List<ProductMonitorEntity>>
    fun getProductMonitorsByUserToken(userToken: String): Flow<List<ProductMonitorEntity>>
    fun getProductMonitorsByStatus(status: String): Flow<List<ProductMonitorEntity>>
    fun getActiveProductMonitors(): Flow<List<ProductMonitorEntity>>
    suspend fun getProductMonitorByUserAndProduct(userToken: String, productId: String): ProductMonitorEntity?
    suspend fun insertProductMonitor(monitor: ProductMonitorEntity)
    suspend fun insertProductMonitors(monitors: List<ProductMonitorEntity>)
    suspend fun updateProductMonitor(monitor: ProductMonitorEntity)
    suspend fun deleteProductMonitor(monitor: ProductMonitorEntity)
    suspend fun deleteProductMonitorById(monitorId: Long)
    suspend fun deleteProductMonitorsByUserToken(userToken: String)
    suspend fun deleteAllProductMonitors()
    suspend fun updateLastCheckInfo(monitorId: Long, checkTime: Date, result: String)
    suspend fun getProductMonitorCount(): Int
    
    // 购物车监控相关操作
    fun getAllCartMonitors(): Flow<List<CartMonitorEntity>>
    fun getCartMonitorById(cartMonitorId: Long): Flow<CartMonitorEntity?>
    fun getCartMonitorsByProductId(productId: String): Flow<List<CartMonitorEntity>>
    fun getCartMonitorsByUserToken(userToken: String): Flow<List<CartMonitorEntity>>
    fun getCartMonitorsByCartId(cartId: String): Flow<List<CartMonitorEntity>>
    fun getCartMonitorsByStatus(status: String): Flow<List<CartMonitorEntity>>
    fun getActiveCartMonitors(): Flow<List<CartMonitorEntity>>
    suspend fun getCartMonitorByUserAndProduct(userToken: String, productId: String): CartMonitorEntity?
    suspend fun insertCartMonitor(monitor: CartMonitorEntity)
    suspend fun insertCartMonitors(monitors: List<CartMonitorEntity>)
    suspend fun updateCartMonitor(monitor: CartMonitorEntity)
    suspend fun deleteCartMonitor(monitor: CartMonitorEntity)
    suspend fun deleteCartMonitorById(cartMonitorId: Long)
    suspend fun deleteCartMonitorsByUserToken(userToken: String)
    suspend fun deleteAllCartMonitors()
    suspend fun updateCartLastCheckInfo(cartMonitorId: Long, checkTime: Date, result: String)
    suspend fun getCartMonitorCount(): Int
    
    // 监控记录相关操作
    fun getAllMonitorRecords(): Flow<List<MonitorRecordEntity>>
    fun getMonitorRecordById(recordId: Long): Flow<MonitorRecordEntity?>
    fun getMonitorRecordsByProductId(productId: String): Flow<List<MonitorRecordEntity>>
    fun getMonitorRecordsByUserToken(userToken: String): Flow<List<MonitorRecordEntity>>
    fun getMonitorRecordsByType(monitorType: String): Flow<List<MonitorRecordEntity>>
    fun getMonitorRecordsByEventType(eventType: String): Flow<List<MonitorRecordEntity>>
    fun getMonitorRecordsByTimeRange(startTime: Date, endTime: Date): Flow<List<MonitorRecordEntity>>
    fun getMonitorRecordsByProductAndTimeRange(productId: String, startTime: Date, endTime: Date): Flow<List<MonitorRecordEntity>>
    suspend fun insertMonitorRecord(record: MonitorRecordEntity)
    suspend fun insertMonitorRecords(records: List<MonitorRecordEntity>)
    suspend fun updateMonitorRecord(record: MonitorRecordEntity)
    suspend fun deleteMonitorRecord(record: MonitorRecordEntity)
    suspend fun deleteMonitorRecordById(recordId: Long)
    suspend fun deleteMonitorRecordsByProductId(productId: String)
    suspend fun deleteMonitorRecordsByUserToken(userToken: String)
    suspend fun deleteMonitorRecordsBeforeTime(beforeTime: Date)
    suspend fun deleteAllMonitorRecords()
    suspend fun getMonitorRecordCount(): Int
    suspend fun getMonitorRecordCountByProductId(productId: String): Int
}
