package dev.pigmomo.yhkit2025.data.repository

import dev.pigmomo.yhkit2025.data.dao.ProductDao
import dev.pigmomo.yhkit2025.data.dao.ProductMonitorDao
import dev.pigmomo.yhkit2025.data.dao.CartMonitorDao
import dev.pigmomo.yhkit2025.data.dao.MonitorRecordDao
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.CartMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitorRecordEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import java.util.Date

/**
 * 商品监控数据仓库实现类
 */
class ProductMonitorRepositoryImpl(
    private val productDao: ProductDao? = null,
    private val productMonitorDao: ProductMonitorDao? = null,
    private val cartMonitorDao: CartMonitorDao? = null,
    private val monitorRecordDao: MonitorRecordDao? = null
) : ProductMonitorRepository {
    
    // 商品相关操作实现
    override fun getAllProducts(): Flow<List<ProductEntity>> =
        productDao?.getAllProducts() ?: flowOf(emptyList())
    
    override fun getProductById(productId: String): Flow<ProductEntity?> =
        productDao?.getProductById(productId) ?: flowOf(null)
    
    override suspend fun getProductByIdSync(productId: String): ProductEntity? =
        productDao?.getProductByIdSync(productId)
    
    override fun searchProductsByName(name: String): Flow<List<ProductEntity>> =
        productDao?.searchProductsByName(name) ?: flowOf(emptyList())
    
    override fun getProductsByCategory(category: String): Flow<List<ProductEntity>> =
        productDao?.getProductsByCategory(category) ?: flowOf(emptyList())
    
    override fun getProductsByBrand(brand: String): Flow<List<ProductEntity>> =
        productDao?.getProductsByBrand(brand) ?: flowOf(emptyList())
    
    override suspend fun insertProduct(product: ProductEntity) {
        productDao?.insertProduct(product)
    }
    
    override suspend fun insertProducts(products: List<ProductEntity>) {
        productDao?.insertProducts(products)
    }
    
    override suspend fun updateProduct(product: ProductEntity) {
        productDao?.updateProduct(product)
    }
    
    override suspend fun deleteProduct(product: ProductEntity) {
        productDao?.deleteProduct(product)
    }
    
    override suspend fun deleteProductById(productId: String) {
        productDao?.deleteProductById(productId)
    }
    
    override suspend fun deleteAllProducts() {
        productDao?.deleteAllProducts()
    }
    
    override suspend fun getProductCount(): Int =
        productDao?.getProductCount() ?: 0
    
    // 商品监控相关操作实现
    override fun getAllProductMonitors(): Flow<List<ProductMonitorEntity>> =
        productMonitorDao?.getAllProductMonitors() ?: flowOf(emptyList())
    
    override fun getProductMonitorById(monitorId: Long): Flow<ProductMonitorEntity?> =
        productMonitorDao?.getProductMonitorById(monitorId) ?: flowOf(null)
    
    override fun getProductMonitorsByProductId(productId: String): Flow<List<ProductMonitorEntity>> =
        productMonitorDao?.getProductMonitorsByProductId(productId) ?: flowOf(emptyList())
    
    override fun getProductMonitorsByUserToken(userToken: String): Flow<List<ProductMonitorEntity>> =
        productMonitorDao?.getProductMonitorsByUserToken(userToken) ?: flowOf(emptyList())
    
    override fun getProductMonitorsByStatus(status: String): Flow<List<ProductMonitorEntity>> =
        productMonitorDao?.getProductMonitorsByStatus(status) ?: flowOf(emptyList())
    
    override fun getActiveProductMonitors(): Flow<List<ProductMonitorEntity>> =
        productMonitorDao?.getActiveProductMonitors() ?: flowOf(emptyList())
    
    override suspend fun getProductMonitorByUserAndProduct(userToken: String, productId: String): ProductMonitorEntity? =
        productMonitorDao?.getProductMonitorByUserAndProduct(userToken, productId)
    
    override suspend fun insertProductMonitor(monitor: ProductMonitorEntity) {
        productMonitorDao?.insertProductMonitor(monitor)
    }
    
    override suspend fun insertProductMonitors(monitors: List<ProductMonitorEntity>) {
        productMonitorDao?.insertProductMonitors(monitors)
    }
    
    override suspend fun updateProductMonitor(monitor: ProductMonitorEntity) {
        productMonitorDao?.updateProductMonitor(monitor)
    }
    
    override suspend fun deleteProductMonitor(monitor: ProductMonitorEntity) {
        productMonitorDao?.deleteProductMonitor(monitor)
    }
    
    override suspend fun deleteProductMonitorById(monitorId: Long) {
        productMonitorDao?.deleteProductMonitorById(monitorId)
    }
    
    override suspend fun deleteProductMonitorsByUserToken(userToken: String) {
        productMonitorDao?.deleteProductMonitorsByUserToken(userToken)
    }
    
    override suspend fun deleteAllProductMonitors() {
        productMonitorDao?.deleteAllProductMonitors()
    }
    
    override suspend fun updateLastCheckInfo(monitorId: Long, checkTime: Date, result: String) {
        productMonitorDao?.updateLastCheckInfo(monitorId, checkTime, result)
    }
    
    override suspend fun getProductMonitorCount(): Int =
        productMonitorDao?.getProductMonitorCount() ?: 0

    // 购物车监控相关操作实现
    override fun getAllCartMonitors(): Flow<List<CartMonitorEntity>> =
        cartMonitorDao?.getAllCartMonitors() ?: flowOf(emptyList())

    override fun getCartMonitorById(cartMonitorId: Long): Flow<CartMonitorEntity?> =
        cartMonitorDao?.getCartMonitorById(cartMonitorId) ?: flowOf(null)

    override fun getCartMonitorsByProductId(productId: String): Flow<List<CartMonitorEntity>> =
        cartMonitorDao?.getCartMonitorsByProductId(productId) ?: flowOf(emptyList())

    override fun getCartMonitorsByUserToken(userToken: String): Flow<List<CartMonitorEntity>> =
        cartMonitorDao?.getCartMonitorsByUserToken(userToken) ?: flowOf(emptyList())

    override fun getCartMonitorsByCartId(cartId: String): Flow<List<CartMonitorEntity>> =
        cartMonitorDao?.getCartMonitorsByCartId(cartId) ?: flowOf(emptyList())

    override fun getCartMonitorsByStatus(status: String): Flow<List<CartMonitorEntity>> =
        cartMonitorDao?.getCartMonitorsByStatus(status) ?: flowOf(emptyList())

    override fun getActiveCartMonitors(): Flow<List<CartMonitorEntity>> =
        cartMonitorDao?.getActiveCartMonitors() ?: flowOf(emptyList())

    override suspend fun getCartMonitorByUserAndProduct(userToken: String, productId: String): CartMonitorEntity? =
        cartMonitorDao?.getCartMonitorByUserAndProduct(userToken, productId)

    override suspend fun insertCartMonitor(monitor: CartMonitorEntity) {
        cartMonitorDao?.insertCartMonitor(monitor)
    }

    override suspend fun insertCartMonitors(monitors: List<CartMonitorEntity>) {
        cartMonitorDao?.insertCartMonitors(monitors)
    }

    override suspend fun updateCartMonitor(monitor: CartMonitorEntity) {
        cartMonitorDao?.updateCartMonitor(monitor)
    }

    override suspend fun deleteCartMonitor(monitor: CartMonitorEntity) {
        cartMonitorDao?.deleteCartMonitor(monitor)
    }

    override suspend fun deleteCartMonitorById(cartMonitorId: Long) {
        cartMonitorDao?.deleteCartMonitorById(cartMonitorId)
    }

    override suspend fun deleteCartMonitorsByUserToken(userToken: String) {
        cartMonitorDao?.deleteCartMonitorsByUserToken(userToken)
    }

    override suspend fun deleteAllCartMonitors() {
        cartMonitorDao?.deleteAllCartMonitors()
    }

    override suspend fun updateCartLastCheckInfo(cartMonitorId: Long, checkTime: Date, result: String) {
        cartMonitorDao?.updateLastCheckInfo(cartMonitorId, checkTime, result)
    }

    override suspend fun getCartMonitorCount(): Int =
        cartMonitorDao?.getCartMonitorCount() ?: 0

    // 监控记录相关操作实现
    override fun getAllMonitorRecords(): Flow<List<MonitorRecordEntity>> =
        monitorRecordDao?.getAllMonitorRecords() ?: flowOf(emptyList())

    override fun getMonitorRecordById(recordId: Long): Flow<MonitorRecordEntity?> =
        monitorRecordDao?.getMonitorRecordById(recordId) ?: flowOf(null)

    override fun getMonitorRecordsByProductId(productId: String): Flow<List<MonitorRecordEntity>> =
        monitorRecordDao?.getMonitorRecordsByProductId(productId) ?: flowOf(emptyList())

    override fun getMonitorRecordsByUserToken(userToken: String): Flow<List<MonitorRecordEntity>> =
        monitorRecordDao?.getMonitorRecordsByUserToken(userToken) ?: flowOf(emptyList())

    override fun getMonitorRecordsByType(monitorType: String): Flow<List<MonitorRecordEntity>> =
        monitorRecordDao?.getMonitorRecordsByType(monitorType) ?: flowOf(emptyList())

    override fun getMonitorRecordsByEventType(eventType: String): Flow<List<MonitorRecordEntity>> =
        monitorRecordDao?.getMonitorRecordsByEventType(eventType) ?: flowOf(emptyList())

    override fun getMonitorRecordsByTimeRange(startTime: Date, endTime: Date): Flow<List<MonitorRecordEntity>> =
        monitorRecordDao?.getMonitorRecordsByTimeRange(startTime, endTime) ?: flowOf(emptyList())

    override fun getMonitorRecordsByProductAndTimeRange(productId: String, startTime: Date, endTime: Date): Flow<List<MonitorRecordEntity>> =
        monitorRecordDao?.getMonitorRecordsByProductAndTimeRange(productId, startTime, endTime) ?: flowOf(emptyList())

    override suspend fun insertMonitorRecord(record: MonitorRecordEntity) {
        monitorRecordDao?.insertMonitorRecord(record)
    }

    override suspend fun insertMonitorRecords(records: List<MonitorRecordEntity>) {
        monitorRecordDao?.insertMonitorRecords(records)
    }

    override suspend fun updateMonitorRecord(record: MonitorRecordEntity) {
        monitorRecordDao?.updateMonitorRecord(record)
    }

    override suspend fun deleteMonitorRecord(record: MonitorRecordEntity) {
        monitorRecordDao?.deleteMonitorRecord(record)
    }

    override suspend fun deleteMonitorRecordById(recordId: Long) {
        monitorRecordDao?.deleteMonitorRecordById(recordId)
    }

    override suspend fun deleteMonitorRecordsByProductId(productId: String) {
        monitorRecordDao?.deleteMonitorRecordsByProductId(productId)
    }

    override suspend fun deleteMonitorRecordsByUserToken(userToken: String) {
        monitorRecordDao?.deleteMonitorRecordsByUserToken(userToken)
    }

    override suspend fun deleteMonitorRecordsBeforeTime(beforeTime: Date) {
        monitorRecordDao?.deleteMonitorRecordsBeforeTime(beforeTime)
    }

    override suspend fun deleteAllMonitorRecords() {
        monitorRecordDao?.deleteAllMonitorRecords()
    }

    override suspend fun getMonitorRecordCount(): Int =
        monitorRecordDao?.getMonitorRecordCount() ?: 0

    override suspend fun getMonitorRecordCountByProductId(productId: String): Int =
        monitorRecordDao?.getMonitorRecordCountByProductId(productId) ?: 0
}
