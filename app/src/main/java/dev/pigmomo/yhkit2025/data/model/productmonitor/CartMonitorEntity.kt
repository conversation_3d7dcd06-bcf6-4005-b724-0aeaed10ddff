package dev.pigmomo.yhkit2025.data.model.productmonitor

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 购物车监控实体类
 * 监控购物车中商品的变化
 */
@Entity(
    tableName = "cart_monitors",
    foreignKeys = [
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["product_id"],
            childColumns = ["product_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("product_id"), // 加快基于商品ID的查询
        Index("user_token"), // 加快基于用户令牌的查询
        Index("cart_id"), // 加快基于购物车ID的查询
        Index("monitor_status"), // 加快基于监控状态的查询
        Index("create_time") // 加快基于创建时间的查询
    ]
)
data class CartMonitorEntity(
    /**
     * 购物车监控记录ID，主键
     */
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "cart_monitor_id")
    val cartMonitorId: Long = 0,
    
    /**
     * 商品ID
     */
    @ColumnInfo(name = "product_id")
    val productId: String,
    
    /**
     * 用户令牌UID（关联TokenEntity）
     */
    @ColumnInfo(name = "user_token")
    val userToken: String,
    
    /**
     * 用户手机号
     */
    @ColumnInfo(name = "phone_number")
    val phoneNumber: String = "",
    
    /**
     * 购物车ID
     */
    @ColumnInfo(name = "cart_id")
    val cartId: String = "",
    
    /**
     * 购物车中的商品数量
     */
    @ColumnInfo(name = "cart_quantity")
    val cartQuantity: Int = 0,
    
    /**
     * 购物车中商品的选择状态
     */
    @ColumnInfo(name = "select_state")
    val selectState: Int = 0,
    
    /**
     * 监控状态 (ACTIVE, PAUSED, STOPPED)
     */
    @ColumnInfo(name = "monitor_status")
    val monitorStatus: String = "ACTIVE",
    
    /**
     * 是否监控数量变化
     */
    @ColumnInfo(name = "monitor_quantity")
    val monitorQuantity: Boolean = true,
    
    /**
     * 是否监控选择状态变化
     */
    @ColumnInfo(name = "monitor_selection")
    val monitorSelection: Boolean = true,
    
    /**
     * 是否监控商品可用性
     */
    @ColumnInfo(name = "monitor_availability")
    val monitorAvailability: Boolean = true,
    
    /**
     * 最后检查时间
     */
    @ColumnInfo(name = "last_check_time")
    val lastCheckTime: Date? = null,
    
    /**
     * 最后检查结果
     */
    @ColumnInfo(name = "last_check_result")
    val lastCheckResult: String = "",
    
    /**
     * 创建时间
     */
    @ColumnInfo(name = "create_time")
    val createTime: Date = Date(),
    
    /**
     * 更新时间
     */
    @ColumnInfo(name = "update_time")
    val updateTime: Date = Date(),
    
    /**
     * 备注信息
     */
    @ColumnInfo(name = "notes")
    val notes: String = ""
)
