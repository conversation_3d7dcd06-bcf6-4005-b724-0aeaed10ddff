package dev.pigmomo.yhkit2025.data.model.productmonitor

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 监控记录实体类
 * 统一记录所有监控活动的历史数据，用于数据分析
 */
@Entity(
    tableName = "monitor_records",
    foreignKeys = [
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["product_id"],
            childColumns = ["product_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("product_id"), // 加快基于商品ID的查询
        Index("user_token"), // 加快基于用户令牌的查询
        Index("monitor_type"), // 加快基于监控类型的查询
        Index("record_time"), // 加快基于记录时间的查询
        Index("event_type") // 加快基于事件类型的查询
    ]
)
data class MonitorRecordEntity(
    /**
     * 记录ID，主键
     */
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "record_id")
    val recordId: Long = 0,
    
    /**
     * 商品ID
     */
    @ColumnInfo(name = "product_id")
    val productId: String,
    
    /**
     * 用户令牌UID（关联TokenEntity）
     */
    @ColumnInfo(name = "user_token")
    val userToken: String,
    
    /**
     * 用户手机号
     */
    @ColumnInfo(name = "phone_number")
    val phoneNumber: String = "",
    
    /**
     * 监控类型 (PRODUCT, CART)
     */
    @ColumnInfo(name = "monitor_type")
    val monitorType: String,
    
    /**
     * 事件类型 (PRICE_CHANGE, STOCK_CHANGE, CART_CHANGE, CHECK_SUCCESS, CHECK_FAILED)
     */
    @ColumnInfo(name = "event_type")
    val eventType: String,
    
    /**
     * 变化前的值（JSON格式）
     */
    @ColumnInfo(name = "old_value")
    val oldValue: String = "",
    
    /**
     * 变化后的值（JSON格式）
     */
    @ColumnInfo(name = "new_value")
    val newValue: String = "",
    
    /**
     * 价格变化（分）- 仅价格监控时使用
     */
    @ColumnInfo(name = "price_change")
    val priceChange: Int = 0,
    
    /**
     * 库存变化 - 仅库存监控时使用
     */
    @ColumnInfo(name = "stock_change")
    val stockChange: Int = 0,
    
    /**
     * 事件描述
     */
    @ColumnInfo(name = "event_description")
    val eventDescription: String = "",
    
    /**
     * 是否触发了通知
     */
    @ColumnInfo(name = "notification_sent")
    val notificationSent: Boolean = false,
    
    /**
     * 记录时间
     */
    @ColumnInfo(name = "record_time")
    val recordTime: Date = Date(),
    
    /**
     * 额外数据（JSON格式）
     */
    @ColumnInfo(name = "extra_data")
    val extraData: String = ""
)
