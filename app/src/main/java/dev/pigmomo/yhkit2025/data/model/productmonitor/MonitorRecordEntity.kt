package dev.pigmomo.yhkit2025.data.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 监控记录实体类
 * 统一记录所有监控活动的历史数据，用于数据分析
 */
@Entity(
    tableName = "monitor_records",
    foreignKeys = [
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["product_id"],
            childColumns = ["product_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("product_id"), // 加快基于商品ID的查询
        Index("user_token"), // 加快基于用户令牌的查询
        Index("monitor_type"), // 加快基于监控类型的查询
        Index("record_time"), // 加快基于记录时间的查询
        Index("event_type") // 加快基于事件类型的查询
    ]
)
data class MonitorRecordEntity(
    /**
     * 记录ID，主键
     */
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "record_id")
    val recordId: Long = 0,
    
    /**
     * 商品ID，外键
     */
    @ColumnInfo(name = "product_id")
    val productId: String,
    
    /**
     * 用户令牌UID
     */
    @ColumnInfo(name = "user_token")
    val userToken: String,
    
    /**
     * 用户手机号
     */
    @ColumnInfo(name = "phone_number")
    val phoneNumber: String = "",
    
    /**
     * 监控类型 (STOCK, CLEARANCE, ARRIVAL)
     */
    @ColumnInfo(name = "monitor_type")
    val monitorType: String,
    
    /**
     * 事件类型 (CHECK, CHANGE, ALERT, ERROR)
     */
    @ColumnInfo(name = "event_type")
    val eventType: String,
    
    /**
     * 记录时间
     */
    @ColumnInfo(name = "record_time")
    val recordTime: Date = Date(),
    
    /**
     * 旧值（JSON格式存储）
     */
    @ColumnInfo(name = "old_value")
    val oldValue: String = "",
    
    /**
     * 新值（JSON格式存储）
     */
    @ColumnInfo(name = "new_value")
    val newValue: String = "",
    
    /**
     * 变化描述
     */
    @ColumnInfo(name = "change_description")
    val changeDescription: String = "",
    
    /**
     * 是否触发了通知
     */
    @ColumnInfo(name = "notification_sent")
    val notificationSent: Boolean = false,
    
    /**
     * 通知类型
     */
    @ColumnInfo(name = "notification_type")
    val notificationType: String = "",
    
    /**
     * 检查耗时（毫秒）
     */
    @ColumnInfo(name = "check_duration_ms")
    val checkDurationMs: Long = 0,
    
    /**
     * 是否检查成功
     */
    @ColumnInfo(name = "check_success")
    val checkSuccess: Boolean = true,
    
    /**
     * 错误信息
     */
    @ColumnInfo(name = "error_message")
    val errorMessage: String = "",
    
    /**
     * 额外数据（JSON格式）
     */
    @ColumnInfo(name = "extra_data")
    val extraData: String = ""
)

/**
 * 监控类型枚举
 */
enum class MonitorType {
    STOCK,     // 库存监控
    CLEARANCE, // 清仓监控
    ARRIVAL    // 到货监控
}

/**
 * 事件类型枚举
 */
enum class EventType {
    CHECK,  // 检查事件
    CHANGE, // 变化事件
    ALERT,  // 警报事件
    ERROR   // 错误事件
}
