package dev.pigmomo.yhkit2025.utils.productmonitor

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitorRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.repository.ProductMonitorRepository
import dev.pigmomo.yhkit2025.utils.PriceUtils
import dev.pigmomo.yhkit2025.utils.common.DateUtils
import dev.pigmomo.yhkit2025.utils.common.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 商品数据分析工具类
 * 提供商品价格趋势分析、库存变化统计、监控报告生成等功能
 */
object ProductAnalyticsUtils {
    private const val TAG = "ProductAnalyticsUtils"
    
    /**
     * 价格趋势数据类
     */
    data class PriceTrend(
        val productId: String,
        val productName: String,
        val priceHistory: List<PricePoint>,
        val currentPrice: Int,
        val lowestPrice: Int,
        val highestPrice: Int,
        val averagePrice: Double,
        val priceChangePercent: Double,
        val trend: String // UP, DOWN, STABLE
    )
    
    /**
     * 价格点数据类
     */
    data class PricePoint(
        val timestamp: Date,
        val price: Int,
        val formattedPrice: String = PriceUtils.formatPrice(price)
    )
    
    /**
     * 库存统计数据类
     */
    data class StockStatistics(
        val productId: String,
        val productName: String,
        val currentStock: Int,
        val stockHistory: List<StockPoint>,
        val averageStock: Double,
        val stockOutCount: Int,
        val stockInCount: Int,
        val lastStockChange: Date?
    )
    
    /**
     * 库存点数据类
     */
    data class StockPoint(
        val timestamp: Date,
        val stock: Int
    )
    
    /**
     * 监控报告数据类
     */
    data class MonitorReport(
        val reportDate: Date,
        val totalProducts: Int,
        val activeMonitors: Int,
        val priceChanges: Int,
        val stockChanges: Int,
        val notifications: Int,
        val topPriceDrops: List<PriceTrend>,
        val topStockChanges: List<StockStatistics>,
        val summary: String
    )
    
    /**
     * 分析商品价格趋势
     */
    suspend fun analyzePriceTrend(
        productId: String,
        days: Int = 30,
        repository: ProductMonitorRepository
    ): PriceTrend? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Analyzing price trend for product: $productId")
            
            // 获取商品信息
            val product = repository.getProductByIdSync(productId)
            if (product == null) {
                Log.w(TAG, "Product not found: $productId")
                return@withContext null
            }
            
            // 计算时间范围
            val endTime = Date()
            val startTime = Calendar.getInstance().apply {
                time = endTime
                add(Calendar.DAY_OF_YEAR, -days)
            }.time
            
            // 获取价格变化记录
            val records = repository.getMonitorRecordsByProductAndTimeRange(productId, startTime, endTime).first()
            val priceRecords = records.filter { it.eventType == "PRICE_CHANGE" }
            
            if (priceRecords.isEmpty()) {
                Log.d(TAG, "No price change records found for product: $productId")
                return@withContext PriceTrend(
                    productId = productId,
                    productName = product.productName,
                    priceHistory = listOf(PricePoint(product.updateTime, product.currentPrice)),
                    currentPrice = product.currentPrice,
                    lowestPrice = product.currentPrice,
                    highestPrice = product.currentPrice,
                    averagePrice = product.currentPrice.toDouble(),
                    priceChangePercent = 0.0,
                    trend = "STABLE"
                )
            }
            
            // 构建价格历史
            val priceHistory = mutableListOf<PricePoint>()
            priceRecords.forEach { record ->
                try {
                    val newValue = JSONObject(record.newValue)
                    val price = newValue.optInt("price", 0)
                    if (price > 0) {
                        priceHistory.add(PricePoint(record.recordTime, price))
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to parse price record: ${record.recordId}", e)
                }
            }
            
            // 添加当前价格
            priceHistory.add(PricePoint(product.updateTime, product.currentPrice))
            priceHistory.sortBy { it.timestamp }
            
            // 计算统计数据
            val prices = priceHistory.map { it.price }
            val lowestPrice = prices.minOrNull() ?: product.currentPrice
            val highestPrice = prices.maxOrNull() ?: product.currentPrice
            val averagePrice = prices.average()
            
            // 计算价格变化百分比
            val firstPrice = priceHistory.firstOrNull()?.price ?: product.currentPrice
            val priceChangePercent = if (firstPrice > 0) {
                ((product.currentPrice - firstPrice).toDouble() / firstPrice) * 100
            } else 0.0
            
            // 判断趋势
            val trend = when {
                priceChangePercent > 5 -> "UP"
                priceChangePercent < -5 -> "DOWN"
                else -> "STABLE"
            }
            
            PriceTrend(
                productId = productId,
                productName = product.productName,
                priceHistory = priceHistory,
                currentPrice = product.currentPrice,
                lowestPrice = lowestPrice,
                highestPrice = highestPrice,
                averagePrice = averagePrice,
                priceChangePercent = priceChangePercent,
                trend = trend
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing price trend for product: $productId", e)
            null
        }
    }
    
    /**
     * 分析库存统计
     */
    suspend fun analyzeStockStatistics(
        productId: String,
        days: Int = 30,
        repository: ProductMonitorRepository
    ): StockStatistics? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Analyzing stock statistics for product: $productId")
            
            // 获取商品信息
            val product = repository.getProductByIdSync(productId)
            if (product == null) {
                Log.w(TAG, "Product not found: $productId")
                return@withContext null
            }
            
            // 计算时间范围
            val endTime = Date()
            val startTime = Calendar.getInstance().apply {
                time = endTime
                add(Calendar.DAY_OF_YEAR, -days)
            }.time
            
            // 获取库存变化记录
            val records = repository.getMonitorRecordsByProductAndTimeRange(productId, startTime, endTime).first()
            val stockRecords = records.filter { it.eventType == "STOCK_CHANGE" }
            
            // 构建库存历史
            val stockHistory = mutableListOf<StockPoint>()
            var stockOutCount = 0
            var stockInCount = 0
            
            stockRecords.forEach { record ->
                try {
                    val newValue = JSONObject(record.newValue)
                    val stock = newValue.optInt("stock", 0)
                    stockHistory.add(StockPoint(record.recordTime, stock))
                    
                    // 统计缺货和补货次数
                    if (record.stockChange < 0) stockOutCount++
                    if (record.stockChange > 0) stockInCount++
                    
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to parse stock record: ${record.recordId}", e)
                }
            }
            
            // 添加当前库存
            stockHistory.add(StockPoint(product.updateTime, product.stockQuantity))
            stockHistory.sortBy { it.timestamp }
            
            // 计算平均库存
            val averageStock = if (stockHistory.isNotEmpty()) {
                stockHistory.map { it.stock }.average()
            } else product.stockQuantity.toDouble()
            
            // 获取最后一次库存变化时间
            val lastStockChange = stockRecords.maxByOrNull { it.recordTime }?.recordTime
            
            StockStatistics(
                productId = productId,
                productName = product.productName,
                currentStock = product.stockQuantity,
                stockHistory = stockHistory,
                averageStock = averageStock,
                stockOutCount = stockOutCount,
                stockInCount = stockInCount,
                lastStockChange = lastStockChange
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing stock statistics for product: $productId", e)
            null
        }
    }
    
    /**
     * 生成监控报告
     */
    suspend fun generateMonitorReport(
        userToken: String? = null,
        days: Int = 7,
        repository: ProductMonitorRepository
    ): MonitorReport = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating monitor report for ${days} days")
            
            // 计算时间范围
            val endTime = Date()
            val startTime = Calendar.getInstance().apply {
                time = endTime
                add(Calendar.DAY_OF_YEAR, -days)
            }.time
            
            // 获取统计数据
            val allProducts = repository.getAllProducts().first()
            val totalProducts = allProducts.size
            
            val allMonitors = if (userToken != null) {
                repository.getProductMonitorsByUserToken(userToken).first()
            } else {
                repository.getAllProductMonitors().first()
            }
            val activeMonitors = allMonitors.count { it.monitorStatus == "ACTIVE" }
            
            val records = if (userToken != null) {
                repository.getMonitorRecordsByUserToken(userToken).first()
            } else {
                repository.getAllMonitorRecords().first()
            }
            
            val recentRecords = records.filter { it.recordTime.after(startTime) }
            val priceChanges = recentRecords.count { it.eventType == "PRICE_CHANGE" }
            val stockChanges = recentRecords.count { it.eventType == "STOCK_CHANGE" }
            val notifications = recentRecords.count { it.notificationSent }
            
            // 获取价格降幅最大的商品
            val topPriceDrops = mutableListOf<PriceTrend>()
            allProducts.take(5).forEach { product ->
                val trend = analyzePriceTrend(product.productId, days, repository)
                if (trend != null && trend.priceChangePercent < -5) {
                    topPriceDrops.add(trend)
                }
            }
            topPriceDrops.sortBy { it.priceChangePercent }
            
            // 获取库存变化最大的商品
            val topStockChanges = mutableListOf<StockStatistics>()
            allProducts.take(5).forEach { product ->
                val stats = analyzeStockStatistics(product.productId, days, repository)
                if (stats != null && (stats.stockOutCount > 0 || stats.stockInCount > 0)) {
                    topStockChanges.add(stats)
                }
            }
            topStockChanges.sortByDescending { it.stockOutCount + it.stockInCount }
            
            // 生成摘要
            val summary = buildString {
                appendLine("监控报告摘要（最近${days}天）：")
                appendLine("- 监控商品总数：$totalProducts")
                appendLine("- 活跃监控数：$activeMonitors")
                appendLine("- 价格变化次数：$priceChanges")
                appendLine("- 库存变化次数：$stockChanges")
                appendLine("- 发送通知数：$notifications")
                if (topPriceDrops.isNotEmpty()) {
                    appendLine("- 最大降价：${topPriceDrops.first().productName} (${String.format("%.1f", topPriceDrops.first().priceChangePercent)}%)")
                }
            }
            
            MonitorReport(
                reportDate = endTime,
                totalProducts = totalProducts,
                activeMonitors = activeMonitors,
                priceChanges = priceChanges,
                stockChanges = stockChanges,
                notifications = notifications,
                topPriceDrops = topPriceDrops.take(3),
                topStockChanges = topStockChanges.take(3),
                summary = summary
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error generating monitor report", e)
            MonitorReport(
                reportDate = Date(),
                totalProducts = 0,
                activeMonitors = 0,
                priceChanges = 0,
                stockChanges = 0,
                notifications = 0,
                topPriceDrops = emptyList(),
                topStockChanges = emptyList(),
                summary = "生成报告时发生错误: ${e.message}"
            )
        }
    }

    /**
     * 导出监控数据为CSV格式
     */
    suspend fun exportMonitorDataToCSV(
        context: Context,
        userToken: String? = null,
        days: Int = 30,
        repository: ProductMonitorRepository
    ): String? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Exporting monitor data to CSV")

            // 计算时间范围
            val endTime = Date()
            val startTime = Calendar.getInstance().apply {
                time = endTime
                add(Calendar.DAY_OF_YEAR, -days)
            }.time

            // 获取监控记录
            val records = if (userToken != null) {
                repository.getMonitorRecordsByUserToken(userToken).first()
            } else {
                repository.getAllMonitorRecords().first()
            }

            val recentRecords = records.filter { it.recordTime.after(startTime) }

            // 生成CSV内容
            val csvHeader = "商品ID,商品名称,用户令牌,监控类型,事件类型,旧值,新值,价格变化,库存变化,事件描述,记录时间,是否通知"
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

            val csvRows = recentRecords.map { record ->
                // 获取商品名称
                val product = repository.getProductByIdSync(record.productId)
                val productName = product?.productName ?: "未知商品"

                "${record.productId},$productName,${record.userToken},${record.monitorType}," +
                        "${record.eventType},\"${record.oldValue}\",\"${record.newValue}\"," +
                        "${record.priceChange},${record.stockChange},\"${record.eventDescription}\"," +
                        "${dateFormat.format(record.recordTime)},${if (record.notificationSent) "是" else "否"}"
            }

            val csvContent = (listOf(csvHeader) + csvRows).joinToString("\n")

            // 保存文件
            val timestamp = DateUtils.formatDateTime(System.currentTimeMillis(), "yyyyMMdd_HHmmss")
            val fileName = "monitor_data_$timestamp.csv"

            val filePath = FileUtils.saveTextToPublicDownloads(context, fileName, csvContent, "ProductMonitor")
                ?: FileUtils.saveTextToDownloads(context, fileName, csvContent, "ProductMonitor")

            Log.d(TAG, "Monitor data exported to: $filePath")
            filePath

        } catch (e: Exception) {
            Log.e(TAG, "Error exporting monitor data to CSV", e)
            null
        }
    }

    /**
     * 生成JSON格式的分析报告
     */
    suspend fun generateAnalyticsJSON(
        userToken: String? = null,
        days: Int = 30,
        repository: ProductMonitorRepository
    ): JSONObject = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating analytics JSON")

            val report = generateMonitorReport(userToken, days, repository)

            val json = JSONObject().apply {
                put("reportDate", SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(report.reportDate))
                put("totalProducts", report.totalProducts)
                put("activeMonitors", report.activeMonitors)
                put("priceChanges", report.priceChanges)
                put("stockChanges", report.stockChanges)
                put("notifications", report.notifications)
                put("summary", report.summary)

                // 价格降幅排行
                val priceDropsArray = JSONArray()
                report.topPriceDrops.forEach { trend ->
                    val trendJson = JSONObject().apply {
                        put("productId", trend.productId)
                        put("productName", trend.productName)
                        put("currentPrice", PriceUtils.formatPrice(trend.currentPrice))
                        put("lowestPrice", PriceUtils.formatPrice(trend.lowestPrice))
                        put("highestPrice", PriceUtils.formatPrice(trend.highestPrice))
                        put("priceChangePercent", String.format("%.2f", trend.priceChangePercent))
                        put("trend", trend.trend)
                    }
                    priceDropsArray.put(trendJson)
                }
                put("topPriceDrops", priceDropsArray)

                // 库存变化排行
                val stockChangesArray = JSONArray()
                report.topStockChanges.forEach { stats ->
                    val statsJson = JSONObject().apply {
                        put("productId", stats.productId)
                        put("productName", stats.productName)
                        put("currentStock", stats.currentStock)
                        put("averageStock", String.format("%.1f", stats.averageStock))
                        put("stockOutCount", stats.stockOutCount)
                        put("stockInCount", stats.stockInCount)
                    }
                    stockChangesArray.put(statsJson)
                }
                put("topStockChanges", stockChangesArray)
            }

            json

        } catch (e: Exception) {
            Log.e(TAG, "Error generating analytics JSON", e)
            JSONObject().apply {
                put("error", "生成分析报告时发生错误: ${e.message}")
                put("timestamp", System.currentTimeMillis())
            }
        }
    }
}
