package dev.pigmomo.yhkit2025.utils.productmonitor

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.TokenEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.repository.ProductMonitorRepository
import dev.pigmomo.yhkit2025.data.repository.ProductMonitorRepositoryImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date

/**
 * 商品监控管理器
 * 统一管理商品监控功能，提供高级API
 */
class ProductMonitorManager private constructor(
    private val repository: ProductMonitorRepository
) {
    private val TAG = "ProductMonitorManager"
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    companion object {
        @Volatile
        private var INSTANCE: ProductMonitorManager? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): ProductMonitorManager {
            return INSTANCE ?: synchronized(this) {
                val database = AppDatabase.getDatabase(context.applicationContext)
                val repository = ProductMonitorRepositoryImpl(
                    productDao = database.productDao(),
                    productMonitorDao = database.productMonitorDao(),
                    cartMonitorDao = database.cartMonitorDao(),
                    monitorRecordDao = database.monitorRecordDao()
                )

                val instance = ProductMonitorManager(repository)
                INSTANCE = instance
                instance
            }
        }
    }
    
    /**
     * 添加商品监控
     */
    suspend fun addProductMonitor(
        productId: String,
        productName: String,
        userToken: String,
        phoneNumber: String,
        monitorPrice: Boolean = true,
        monitorStock: Boolean = true,
        targetPrice: Int = 0,
        priceThreshold: Int = 0,
        checkInterval: Int = 30,
        notificationEnabled: Boolean = true
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding product monitor for: $productId")
            
            // 检查是否已存在监控
            val existingMonitor = repository.getProductMonitorByUserAndProduct(userToken, productId)
            if (existingMonitor != null) {
                Log.w(TAG, "Monitor already exists for product: $productId")
                return@withContext false
            }
            
            // 创建监控记录
            val monitor = ProductMonitorEntity(
                productId = productId,
                userToken = userToken,
                phoneNumber = phoneNumber,
                monitorStatus = "ACTIVE",
                monitorType = when {
                    monitorPrice && monitorStock -> "BOTH"
                    monitorPrice -> "PRICE"
                    monitorStock -> "STOCK"
                    else -> "BOTH"
                },
                targetPrice = targetPrice,
                priceThreshold = priceThreshold,
                monitorStock = monitorStock,
                monitorPrice = monitorPrice,
                checkInterval = checkInterval,
                notificationEnabled = notificationEnabled,
                createTime = Date(),
                updateTime = Date()
            )
            
            repository.insertProductMonitor(monitor)
            
            // 记录监控创建事件
            ProductMonitorUtils.createMonitorRecord(
                productId = productId,
                userToken = userToken,
                phoneNumber = phoneNumber,
                monitorType = "PRODUCT",
                eventType = "MONITOR_CREATED",
                description = "创建商品监控: $productName",
                repository = repository
            )
            
            Log.d(TAG, "Product monitor added successfully for: $productId")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "Error adding product monitor for: $productId", e)
            false
        }
    }
    
    /**
     * 从购物车商品批量添加监控
     */
    suspend fun addMonitorsFromCart(
        cartProducts: List<Product>,
        userToken: String,
        phoneNumber: String
    ): Int = withContext(Dispatchers.IO) {
        var successCount = 0
        
        cartProducts.forEach { product ->
            try {
                // 先添加或更新商品信息
                val productEntity = ProductMonitorUtils.createProductFromCartItem(product)
                val existingProduct = repository.getProductByIdSync(product.id)
                
                if (existingProduct == null) {
                    repository.insertProduct(productEntity)
                } else {
                    repository.updateProduct(productEntity)
                }
                
                // 添加商品监控
                val success = addProductMonitor(
                    productId = product.id,
                    productName = product.title,
                    userToken = userToken,
                    phoneNumber = phoneNumber,
                    monitorPrice = true,
                    monitorStock = true,
                    targetPrice = 0,
                    priceThreshold = 100, // 默认1元变化阈值
                    checkInterval = 30,
                    notificationEnabled = true
                )
                
                if (success) successCount++
                
                // 添加购物车监控
                CartMonitorUtils.createCartMonitor(
                    productId = product.id,
                    userToken = userToken,
                    phoneNumber = phoneNumber,
                    cartId = "",
                    initialQuantity = product.num,
                    initialSelection = product.selectstate,
                    repository = repository
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Error adding monitor for product: ${product.id}", e)
            }
        }
        
        Log.d(TAG, "Added $successCount monitors from ${cartProducts.size} cart products")
        successCount
    }
    
    /**
     * 执行监控检查
     */
    fun performMonitorCheck(userToken: String? = null) {
        scope.launch {
            try {
                Log.d(TAG, "Starting monitor check")
                
                // 获取活跃的监控
                val monitors = if (userToken != null) {
                    repository.getProductMonitorsByUserToken(userToken).first()
                        .filter { it.monitorStatus == "ACTIVE" }
                } else {
                    repository.getActiveProductMonitors().first()
                }
                
                Log.d(TAG, "Found ${monitors.size} active monitors")
                
                monitors.forEach { monitor ->
                    try {
                        // 这里需要获取对应的TokenEntity
                        // 实际实现时需要从TokenRepository获取
                        // val token = tokenRepository.getTokenByUid(monitor.userToken)
                        
                        // 暂时跳过实际检查，因为需要TokenEntity
                        Log.d(TAG, "Checking monitor for product: ${monitor.productId}")
                        
                        // 更新最后检查时间
                        repository.updateLastCheckInfo(
                            monitor.monitorId,
                            Date(),
                            "检查完成"
                        )
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "Error checking monitor: ${monitor.monitorId}", e)
                        
                        // 记录检查失败
                        repository.updateLastCheckInfo(
                            monitor.monitorId,
                            Date(),
                            "检查失败: ${e.message}"
                        )
                    }
                }
                
                Log.d(TAG, "Monitor check completed")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error performing monitor check", e)
            }
        }
    }
    
    /**
     * 获取用户的监控统计
     */
    suspend fun getUserMonitorStats(userToken: String): Map<String, Any> = withContext(Dispatchers.IO) {
        try {
            val productMonitors = repository.getProductMonitorsByUserToken(userToken).first()
            val cartMonitors = repository.getCartMonitorsByUserToken(userToken).first()
            val records = repository.getMonitorRecordsByUserToken(userToken).first()
            
            mapOf(
                "totalProductMonitors" to productMonitors.size,
                "activeProductMonitors" to productMonitors.count { it.monitorStatus == "ACTIVE" },
                "totalCartMonitors" to cartMonitors.size,
                "activeCartMonitors" to cartMonitors.count { it.monitorStatus == "ACTIVE" },
                "totalRecords" to records.size,
                "recentRecords" to records.take(10).size,
                "priceChangeRecords" to records.count { it.eventType == "PRICE_CHANGE" },
                "stockChangeRecords" to records.count { it.eventType == "STOCK_CHANGE" },
                "notificationsSent" to records.count { it.notificationSent }
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user monitor stats", e)
            emptyMap()
        }
    }
    
    /**
     * 生成监控报告
     */
    suspend fun generateReport(userToken: String? = null, days: Int = 7): String = withContext(Dispatchers.IO) {
        try {
            val report = ProductAnalyticsUtils.generateMonitorReport(userToken, days, repository)
            report.summary
            
        } catch (e: Exception) {
            Log.e(TAG, "Error generating report", e)
            "生成报告时发生错误: ${e.message}"
        }
    }
    
    /**
     * 导出监控数据
     */
    suspend fun exportData(context: Context, userToken: String? = null, days: Int = 30): String? = withContext(Dispatchers.IO) {
        try {
            ProductAnalyticsUtils.exportMonitorDataToCSV(context, userToken, days, repository)

        } catch (e: Exception) {
            Log.e(TAG, "Error exporting data", e)
            null
        }
    }
    
    /**
     * 清理过期数据
     */
    suspend fun cleanupOldData(daysToKeep: Int = 90): Int = withContext(Dispatchers.IO) {
        try {
            val cutoffDate = Date(System.currentTimeMillis() - daysToKeep * 24 * 60 * 60 * 1000L)
            
            val recordCountBefore = repository.getMonitorRecordCount()
            repository.deleteMonitorRecordsBeforeTime(cutoffDate)
            val recordCountAfter = repository.getMonitorRecordCount()
            
            val deletedCount = recordCountBefore - recordCountAfter
            Log.d(TAG, "Cleaned up $deletedCount old monitor records")
            
            deletedCount
            
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up old data", e)
            0
        }
    }
}
