package dev.pigmomo.yhkit2025.utils.productmonitor

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.data.model.TokenEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitorRecordEntity
import dev.pigmomo.yhkit2025.data.repository.ProductMonitorRepository
import dev.pigmomo.yhkit2025.utils.PriceUtils
import dev.pigmomo.yhkit2025.utils.common.DateUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.Date

/**
 * 商品监控工具类
 * 提供商品信息获取、库存检查、价格变化监控等核心功能
 */
object ProductMonitorUtils {
    private const val TAG = "ProductMonitorUtils"
    
    /**
     * 监控结果数据类
     */
    data class MonitorResult(
        val success: Boolean,
        val message: String,
        val priceChanged: Boolean = false,
        val stockChanged: Boolean = false,
        val oldPrice: Int = 0,
        val newPrice: Int = 0,
        val oldStock: Int = 0,
        val newStock: Int = 0,
        val extraData: Map<String, Any> = emptyMap()
    )
    
    /**
     * 商品信息数据类
     */
    data class ProductInfo(
        val productId: String,
        val productName: String,
        val currentPrice: Int,
        val originalPrice: Int,
        val stockQuantity: Int,
        val inStock: Boolean,
        val status: String,
        val imageUrl: String = "",
        val detailUrl: String = "",
        val storeId: String = "",
        val storeName: String = "",
        val specInfo: String = "",
        val skuCode: String = ""
    )
    
    /**
     * 从购物车商品创建ProductEntity
     */
    fun createProductFromCartItem(cartProduct: Product): ProductEntity {
        return ProductEntity(
            productId = cartProduct.id,
            productName = cartProduct.title,
            description = cartProduct.subtitle,
            imageUrl = cartProduct.imgurl,
            currentPrice = cartProduct.price?.price ?: 0,
            originalPrice = cartProduct.price?.originalprice ?: 0,
            stockQuantity = cartProduct.stocknum,
            inStock = cartProduct.available == 1,
            status = if (cartProduct.available == 1) "NORMAL" else "OUT_OF_STOCK",
            storeId = cartProduct.sellercategory,
            specInfo = cartProduct.spec?.let { spec ->
                JSONObject().apply {
                    put("spec", spec.toString())
                }.toString()
            } ?: "",
            skuCode = cartProduct.originalskucode,
            createTime = Date(),
            updateTime = Date()
        )
    }
    
    /**
     * 检查商品信息变化
     */
    suspend fun checkProductChanges(
        context: Context,
        productId: String,
        token: TokenEntity,
        repository: ProductMonitorRepository
    ): MonitorResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Checking product changes for: $productId")
            
            // 获取当前存储的商品信息
            val existingProduct = repository.getProductByIdSync(productId)
            if (existingProduct == null) {
                Log.w(TAG, "Product not found in database: $productId")
                return@withContext MonitorResult(
                    success = false,
                    message = "商品不存在于数据库中"
                )
            }
            
            // 获取最新的商品信息（这里需要根据实际API实现）
            val latestProductInfo = fetchLatestProductInfo(context, productId, token)
            if (latestProductInfo == null) {
                Log.e(TAG, "Failed to fetch latest product info for: $productId")
                return@withContext MonitorResult(
                    success = false,
                    message = "获取最新商品信息失败"
                )
            }
            
            // 比较价格和库存变化
            val priceChanged = existingProduct.currentPrice != latestProductInfo.currentPrice
            val stockChanged = existingProduct.stockQuantity != latestProductInfo.stockQuantity
            
            // 更新商品信息
            if (priceChanged || stockChanged) {
                val updatedProduct = existingProduct.copy(
                    currentPrice = latestProductInfo.currentPrice,
                    originalPrice = latestProductInfo.originalPrice,
                    stockQuantity = latestProductInfo.stockQuantity,
                    inStock = latestProductInfo.inStock,
                    status = latestProductInfo.status,
                    updateTime = Date()
                )
                repository.updateProduct(updatedProduct)
                
                Log.d(TAG, "Product updated: $productId, price changed: $priceChanged, stock changed: $stockChanged")
            }
            
            MonitorResult(
                success = true,
                message = "检查完成",
                priceChanged = priceChanged,
                stockChanged = stockChanged,
                oldPrice = existingProduct.currentPrice,
                newPrice = latestProductInfo.currentPrice,
                oldStock = existingProduct.stockQuantity,
                newStock = latestProductInfo.stockQuantity,
                extraData = mapOf(
                    "productName" to existingProduct.productName,
                    "oldPriceFormatted" to PriceUtils.formatPrice(existingProduct.currentPrice),
                    "newPriceFormatted" to PriceUtils.formatPrice(latestProductInfo.currentPrice)
                )
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error checking product changes for: $productId", e)
            MonitorResult(
                success = false,
                message = "检查商品变化时发生错误: ${e.message}"
            )
        }
    }
    
    /**
     * 获取最新的商品信息
     * 这里需要根据实际的API接口实现
     */
    private suspend fun fetchLatestProductInfo(
        context: Context,
        productId: String,
        token: TokenEntity
    ): ProductInfo? = withContext(Dispatchers.IO) {
        try {
            // TODO: 实现实际的API调用获取商品信息
            // 这里是示例实现，需要根据实际API调整
            
            Log.d(TAG, "Fetching latest product info for: $productId")
            
            // 示例：通过购物车API获取商品信息
            // 实际实现时需要调用具体的商品详情API
            
            null // 暂时返回null，需要实际实现
            
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching latest product info for: $productId", e)
            null
        }
    }
    
    /**
     * 创建监控记录
     */
    suspend fun createMonitorRecord(
        productId: String,
        userToken: String,
        phoneNumber: String,
        monitorType: String,
        eventType: String,
        oldValue: String = "",
        newValue: String = "",
        priceChange: Int = 0,
        stockChange: Int = 0,
        description: String = "",
        notificationSent: Boolean = false,
        extraData: String = "",
        repository: ProductMonitorRepository
    ) {
        try {
            val record = MonitorRecordEntity(
                productId = productId,
                userToken = userToken,
                phoneNumber = phoneNumber,
                monitorType = monitorType,
                eventType = eventType,
                oldValue = oldValue,
                newValue = newValue,
                priceChange = priceChange,
                stockChange = stockChange,
                eventDescription = description,
                notificationSent = notificationSent,
                recordTime = Date(),
                extraData = extraData
            )
            
            repository.insertMonitorRecord(record)
            Log.d(TAG, "Monitor record created: $eventType for product $productId")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating monitor record", e)
        }
    }
    
    /**
     * 检查是否需要发送通知
     */
    fun shouldSendNotification(
        monitor: ProductMonitorEntity,
        result: MonitorResult
    ): Boolean {
        if (!monitor.notificationEnabled) return false
        
        // 价格监控通知条件
        if (monitor.monitorPrice && result.priceChanged) {
            // 如果设置了目标价格，检查是否达到目标价格
            if (monitor.targetPrice > 0 && result.newPrice <= monitor.targetPrice) {
                return true
            }
            
            // 如果设置了价格变化阈值，检查价格变化是否超过阈值
            if (monitor.priceThreshold > 0) {
                val priceChange = kotlin.math.abs(result.newPrice - result.oldPrice)
                if (priceChange >= monitor.priceThreshold) {
                    return true
                }
            }
            
            // 默认价格变化就通知
            return true
        }
        
        // 库存监控通知条件
        if (monitor.monitorStock && result.stockChanged) {
            // 库存从无到有
            if (result.oldStock == 0 && result.newStock > 0) {
                return true
            }
            
            // 库存从有到无
            if (result.oldStock > 0 && result.newStock == 0) {
                return true
            }
        }
        
        return false
    }
    
    /**
     * 格式化监控结果消息
     */
    fun formatMonitorMessage(
        productName: String,
        result: MonitorResult
    ): String {
        val messages = mutableListOf<String>()
        
        if (result.priceChanged) {
            val oldPriceStr = PriceUtils.formatPrice(result.oldPrice)
            val newPriceStr = PriceUtils.formatPrice(result.newPrice)
            val changeType = if (result.newPrice < result.oldPrice) "降价" else "涨价"
            messages.add("价格${changeType}: ¥$oldPriceStr → ¥$newPriceStr")
        }
        
        if (result.stockChanged) {
            val stockStatus = if (result.newStock > 0) "有货" else "缺货"
            messages.add("库存变化: ${result.oldStock} → ${result.newStock} ($stockStatus)")
        }
        
        return if (messages.isNotEmpty()) {
            "【$productName】\n${messages.joinToString("\n")}"
        } else {
            "【$productName】无变化"
        }
    }
}
