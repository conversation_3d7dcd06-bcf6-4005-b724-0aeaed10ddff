package dev.pigmomo.yhkit2025.utils.productmonitor

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.cart.CartResponse
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.utils.CartUtils
import dev.pigmomo.yhkit2025.data.model.TokenEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.CartMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductEntity
import dev.pigmomo.yhkit2025.data.repository.ProductMonitorRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.Date

/**
 * 购物车监控工具类
 * 提供购物车商品监控相关功能
 */
object CartMonitorUtils {
    private const val TAG = "CartMonitorUtils"
    
    /**
     * 购物车监控结果数据类
     */
    data class CartMonitorResult(
        val success: Boolean,
        val message: String,
        val quantityChanged: Boolean = false,
        val selectionChanged: Boolean = false,
        val availabilityChanged: Boolean = false,
        val oldQuantity: Int = 0,
        val newQuantity: Int = 0,
        val oldSelection: Int = 0,
        val newSelection: Int = 0,
        val productRemoved: Boolean = false,
        val productAdded: Boolean = false,
        val extraData: Map<String, Any> = emptyMap()
    )
    
    /**
     * 检查购物车变化
     */
    suspend fun checkCartChanges(
        context: Context,
        userToken: String,
        token: TokenEntity,
        repository: ProductMonitorRepository
    ): List<CartMonitorResult> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Checking cart changes for user: $userToken")
            
            // 获取当前购物车信息
            val cartResponse = fetchCartInfo(context, token)
            if (cartResponse == null) {
                Log.e(TAG, "Failed to fetch cart info")
                return@withContext listOf(
                    CartMonitorResult(
                        success = false,
                        message = "获取购物车信息失败"
                    )
                )
            }
            
            // 获取购物车中的商品
            val cartProducts = CartUtils.extractProductsFromCart(cartResponse)
            
            // 获取当前监控的购物车商品
            val existingMonitors = repository.getCartMonitorsByUserToken(userToken)
            val existingMonitorMap = mutableMapOf<String, CartMonitorEntity>()
            
            existingMonitors.collect { monitors ->
                monitors.forEach { monitor ->
                    existingMonitorMap[monitor.productId] = monitor
                }
            }
            
            val results = mutableListOf<CartMonitorResult>()
            
            // 检查现有商品的变化
            cartProducts.forEach { product ->
                val existingMonitor = existingMonitorMap[product.id]
                
                if (existingMonitor != null) {
                    // 检查变化
                    val result = checkSingleProductCartChanges(product, existingMonitor, repository)
                    results.add(result)
                    
                    // 从map中移除已处理的商品
                    existingMonitorMap.remove(product.id)
                } else {
                    // 新增的商品
                    results.add(
                        CartMonitorResult(
                            success = true,
                            message = "发现新商品",
                            productAdded = true,
                            newQuantity = product.num,
                            newSelection = product.selectstate,
                            extraData = mapOf(
                                "productId" to product.id,
                                "productName" to product.title
                            )
                        )
                    )
                }
            }
            
            // 检查被移除的商品
            existingMonitorMap.values.forEach { removedMonitor ->
                results.add(
                    CartMonitorResult(
                        success = true,
                        message = "商品已从购物车移除",
                        productRemoved = true,
                        oldQuantity = removedMonitor.cartQuantity,
                        oldSelection = removedMonitor.selectState,
                        extraData = mapOf(
                            "productId" to removedMonitor.productId
                        )
                    )
                )
                
                // 更新监控状态为停止
                repository.updateCartMonitor(
                    removedMonitor.copy(
                        monitorStatus = "STOPPED",
                        updateTime = Date()
                    )
                )
            }
            
            Log.d(TAG, "Cart check completed, found ${results.size} changes")
            results
            
        } catch (e: Exception) {
            Log.e(TAG, "Error checking cart changes", e)
            listOf(
                CartMonitorResult(
                    success = false,
                    message = "检查购物车变化时发生错误: ${e.message}"
                )
            )
        }
    }
    
    /**
     * 检查单个商品的购物车变化
     */
    private suspend fun checkSingleProductCartChanges(
        product: Product,
        existingMonitor: CartMonitorEntity,
        repository: ProductMonitorRepository
    ): CartMonitorResult {
        try {
            val quantityChanged = existingMonitor.cartQuantity != product.num
            val selectionChanged = existingMonitor.selectState != product.selectstate
            val availabilityChanged = (product.available == 1) != (existingMonitor.cartQuantity > 0)
            
            // 更新监控记录
            if (quantityChanged || selectionChanged) {
                val updatedMonitor = existingMonitor.copy(
                    cartQuantity = product.num,
                    selectState = product.selectstate,
                    lastCheckTime = Date(),
                    lastCheckResult = "检查完成",
                    updateTime = Date()
                )
                repository.updateCartMonitor(updatedMonitor)
            }
            
            return CartMonitorResult(
                success = true,
                message = "检查完成",
                quantityChanged = quantityChanged,
                selectionChanged = selectionChanged,
                availabilityChanged = availabilityChanged,
                oldQuantity = existingMonitor.cartQuantity,
                newQuantity = product.num,
                oldSelection = existingMonitor.selectState,
                newSelection = product.selectstate,
                extraData = mapOf(
                    "productId" to product.id,
                    "productName" to product.title
                )
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error checking single product cart changes", e)
            return CartMonitorResult(
                success = false,
                message = "检查商品变化时发生错误: ${e.message}"
            )
        }
    }
    
    /**
     * 获取购物车信息
     */
    private suspend fun fetchCartInfo(
        context: Context,
        token: TokenEntity
    ): CartResponse? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Fetching cart info")
            
            // TODO: 实现实际的购物车API调用
            // 这里需要根据实际的API接口实现
            
            null // 暂时返回null，需要实际实现
            
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching cart info", e)
            null
        }
    }
    
    /**
     * 创建购物车监控
     */
    suspend fun createCartMonitor(
        productId: String,
        userToken: String,
        phoneNumber: String,
        cartId: String,
        initialQuantity: Int,
        initialSelection: Int,
        repository: ProductMonitorRepository
    ): Boolean {
        return try {
            val monitor = CartMonitorEntity(
                productId = productId,
                userToken = userToken,
                phoneNumber = phoneNumber,
                cartId = cartId,
                cartQuantity = initialQuantity,
                selectState = initialSelection,
                monitorStatus = "ACTIVE",
                monitorQuantity = true,
                monitorSelection = true,
                monitorAvailability = true,
                createTime = Date(),
                updateTime = Date()
            )
            
            repository.insertCartMonitor(monitor)
            Log.d(TAG, "Cart monitor created for product: $productId")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating cart monitor", e)
            false
        }
    }
    
    /**
     * 批量创建购物车监控
     */
    suspend fun createCartMonitorsFromCart(
        cartProducts: List<Product>,
        userToken: String,
        phoneNumber: String,
        repository: ProductMonitorRepository
    ): Int {
        var successCount = 0
        
        cartProducts.forEach { product ->
            try {
                // 先确保商品存在于商品表中
                val existingProduct = repository.getProductByIdSync(product.id)
                if (existingProduct == null) {
                    val productEntity = ProductMonitorUtils.createProductFromCartItem(product)
                    repository.insertProduct(productEntity)
                }
                
                // 创建购物车监控
                val success = createCartMonitor(
                    productId = product.id,
                    userToken = userToken,
                    phoneNumber = phoneNumber,
                    cartId = "", // 如果有购物车ID可以传入
                    initialQuantity = product.num,
                    initialSelection = product.selectstate,
                    repository = repository
                )
                
                if (success) successCount++
                
            } catch (e: Exception) {
                Log.e(TAG, "Error creating cart monitor for product: ${product.id}", e)
            }
        }
        
        Log.d(TAG, "Created $successCount cart monitors out of ${cartProducts.size} products")
        return successCount
    }
    
    /**
     * 格式化购物车监控消息
     */
    fun formatCartMonitorMessage(
        productName: String,
        result: CartMonitorResult
    ): String {
        val messages = mutableListOf<String>()
        
        if (result.productAdded) {
            messages.add("新增到购物车")
        }
        
        if (result.productRemoved) {
            messages.add("从购物车移除")
        }
        
        if (result.quantityChanged) {
            messages.add("数量变化: ${result.oldQuantity} → ${result.newQuantity}")
        }
        
        if (result.selectionChanged) {
            val oldStatus = if (result.oldSelection == 1) "已选中" else "未选中"
            val newStatus = if (result.newSelection == 1) "已选中" else "未选中"
            messages.add("选择状态: $oldStatus → $newStatus")
        }
        
        return if (messages.isNotEmpty()) {
            "【购物车】$productName\n${messages.joinToString("\n")}"
        } else {
            "【购物车】$productName 无变化"
        }
    }
}
